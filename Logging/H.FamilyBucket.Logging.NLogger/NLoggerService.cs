using System;
using System.IO;
using NLog;
using NLog.Config;

namespace H.FamilyBucket.Logging.NLogger
{
    /// <summary>
    /// NLog日志服务实现
    /// </summary>
    public class NLoggerService : ILoggerService
    {
        private readonly Logger _logger;
        private static readonly object _lock = new object();
        private static bool _isConfigured = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="categoryName">日志类别名称</param>
        /// <param name="configuration">日志配置</param>
        public NLoggerService(string categoryName = null, LoggerConfiguration configuration = null)
        {
            InitializeNLog(configuration);
            _logger = LogManager.GetLogger(categoryName ?? "Default");
        }

        /// <summary>
        /// 初始化NLog配置
        /// </summary>
        /// <param name="configuration">日志配置</param>
        private static void InitializeNLog(LoggerConfiguration configuration = null)
        {
            if (_isConfigured) return;

            lock (_lock)
            {
                if (_isConfigured) return;

                configuration ??= new LoggerConfiguration();

                // 如果配置文件存在，则使用配置文件
                if (File.Exists(configuration.ConfigFilePath))
                {
                    LogManager.Configuration = new XmlLoggingConfiguration(configuration.ConfigFilePath);
                }
                else
                {
                    // 如果配置文件不存在，则使用代码配置
                    CreateProgrammaticConfiguration(configuration);
                }

                _isConfigured = true;
            }
        }

        /// <summary>
        /// 创建程序化配置
        /// </summary>
        /// <param name="config">日志配置</param>
        private static void CreateProgrammaticConfiguration(LoggerConfiguration config)
        {
            var configuration = new LoggingConfiguration();

            // 控制台目标
            if (config.EnableConsole)
            {
                var consoleTarget = new NLog.Targets.ConsoleTarget("console")
                {
                    Layout = "${longdate} ${uppercase:${level}} ${logger:shortName=true} ${message} ${exception:format=tostring}"
                };
                configuration.AddTarget(consoleTarget);
                configuration.AddRule(GetNLogLevel(config.ConsoleMinLevel), NLog.LogLevel.Fatal, consoleTarget);
            }

            // 文件目标
            if (config.EnableFile)
            {
                // 日常日志文件
                var dailyFileTarget = new NLog.Targets.FileTarget("dailyfile")
                {
                    FileName = Path.Combine(config.LogDirectory, "daily", "${shortdate}", "daily.log"),
                    Layout = "${longdate} ${uppercase:${level}} ${logger} ${message} ${exception:format=tostring}",
                    ArchiveFileName = Path.Combine(config.LogDirectory, "daily", "${shortdate}", "daily-{#}.log"),
                    ArchiveEvery = NLog.Targets.FileArchivePeriod.Day,
                    ArchiveAboveSize = config.ArchiveAboveSize,
                    ArchiveNumbering = NLog.Targets.ArchiveNumberingMode.Rolling,
                    MaxArchiveFiles = config.MaxArchiveFiles,
                    ConcurrentWrites = true,
                    KeepFileOpen = false,
                    CreateDirs = true,
                    EnableArchiveFileCompression = config.EnableArchiveFileCompression
                };
                configuration.AddTarget(dailyFileTarget);
                configuration.AddRule(NLog.LogLevel.Trace, NLog.LogLevel.Warn, dailyFileTarget);

                // 错误日志文件
                var errorFileTarget = new NLog.Targets.FileTarget("errorfile")
                {
                    FileName = Path.Combine(config.LogDirectory, "error", "${shortdate}", "error.log"),
                    Layout = "${longdate} ${uppercase:${level}} ${logger} ${message} ${exception:format=tostring}",
                    ArchiveFileName = Path.Combine(config.LogDirectory, "error", "${shortdate}", "error-{#}.log"),
                    ArchiveEvery = NLog.Targets.FileArchivePeriod.Day,
                    ArchiveAboveSize = config.ArchiveAboveSize,
                    ArchiveNumbering = NLog.Targets.ArchiveNumberingMode.Rolling,
                    MaxArchiveFiles = config.MaxArchiveFiles,
                    ConcurrentWrites = true,
                    KeepFileOpen = false,
                    CreateDirs = true,
                    EnableArchiveFileCompression = config.EnableArchiveFileCompression
                };
                configuration.AddTarget(errorFileTarget);
                configuration.AddRule(NLog.LogLevel.Error, NLog.LogLevel.Fatal, errorFileTarget);
            }

            LogManager.Configuration = configuration;
        }

        /// <summary>
        /// 转换日志级别
        /// </summary>
        /// <param name="level">自定义日志级别</param>
        /// <returns>NLog日志级别</returns>
        private static NLog.LogLevel GetNLogLevel(LogLevel level)
        {
            return level switch
            {
                LogLevel.Trace => NLog.LogLevel.Trace,
                LogLevel.Debug => NLog.LogLevel.Debug,
                LogLevel.Info => NLog.LogLevel.Info,
                LogLevel.Warn => NLog.LogLevel.Warn,
                LogLevel.Error => NLog.LogLevel.Error,
                LogLevel.Fatal => NLog.LogLevel.Fatal,
                _ => NLog.LogLevel.Info
            };
        }

        public void Trace(string message, params object[] args)
        {
            _logger.Trace(message, args);
        }

        public void Debug(string message, params object[] args)
        {
            _logger.Debug(message, args);
        }

        public void Info(string message, params object[] args)
        {
            _logger.Info(message, args);
        }

        public void Warn(string message, params object[] args)
        {
            _logger.Warn(message, args);
        }

        public void Error(string message, params object[] args)
        {
            _logger.Error(message, args);
        }

        public void Error(Exception exception, string message, params object[] args)
        {
            _logger.Error(exception, message, args);
        }

        public void Fatal(string message, params object[] args)
        {
            _logger.Fatal(message, args);
        }

        public void Fatal(Exception exception, string message, params object[] args)
        {
            _logger.Fatal(exception, message, args);
        }

        public bool IsEnabled(LogLevel level)
        {
            return _logger.IsEnabled(GetNLogLevel(level));
        }
    }
}
